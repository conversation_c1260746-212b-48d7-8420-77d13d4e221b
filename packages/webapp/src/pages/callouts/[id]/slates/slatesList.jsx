import React from 'react';
import PropTypes from 'prop-types';
import { get, truncate } from 'lodash';
import { statusConfig } from 'utils/helper';
// import Image from 'next/image';
import Style from './dashboard.module.scss';
import CustomCarousel from 'sharedComponents/Carousel/customCarousel';
import InlineSvg from 'sharedComponents/inline-svg';

const SlateList = ({
  sectionHeading,
  noProjectsMsg,
  setSlateList,
  isDashboardView,
  sectionId,
  calloutSubmissions = [],
}) => {
  // Support both array and {docs: []} for calloutSubmissions
  const slateListArray = Array.isArray(calloutSubmissions)
    ? calloutSubmissions
    : get(calloutSubmissions, 'docs', []);
  const slateListLength = get(slateListArray, 'length', 0);

  const goToSnap = (item) => {
    const hash = get(item, 'snapshot.hash');
    console.log(item);
    if (!hash) return;
    window.open(
      `/project/snap/${hash}?status=${item.status}&id=${item.id}`,
      '_blank',
    );
  };

  let snapData = [];
  const showSlates = slateListArray.map((item) => {
    const body = get(item, 'snapshot.body');
    if (body) {
      snapData = JSON.parse(body);
    }
    const theme = get(snapData, 'theme');
    const basicInfo = get(item, 'snapshot.basicInfo', false);
    const snapCover = get(item, 'snapshot.cover.coverPic', '');
    const projectTags = get(basicInfo, 'tags', []);

    let coverUrl;
    switch (true) {
      case !!snapCover:
        coverUrl = snapCover;
        break;
      case theme === 'dark' || theme === 'light':
        coverUrl = `${process.env.BucketUrl}ASSETS/darkLightDmDefaultCover.jpg`;
        break;
      default:
        coverUrl = `${process.env.BucketUrl}ASSETS/dmBcsDefaultCover.jpg`;
        break;
    }

    const statusObj = statusConfig[String(item.status)];
    const status = statusObj
      ? { text: statusObj.label, bgColor: statusObj.backgroundColor }
      : null;

    const renderTags = (projectTags) => {
      const tagsToShow = [];
      let remainingTags = projectTags.length;

      projectTags.forEach((tag) => {
        const textLength = tagsToShow.reduce(
          (acc, curr) => acc + curr.text.length,
          0,
        );
        if (textLength + tag.text.length <= 15 && remainingTags > 1) {
          tagsToShow.push(tag);
          remainingTags--;
        } else if (remainingTags === 1) {
          tagsToShow.push(tag);
          remainingTags--;
        }
      });

      return (
        <>
          {tagsToShow.map((tag, index) => (
            <div
              key={index}
              className={`${Style.badgePill}`}
              style={{ width: 'fit-content' }}
            >
              {get(tag, 'text')}
            </div>
          ))}
          {remainingTags > 0 && (
            <div
              className={`${Style.badgePill}`}
              style={{ width: 'fit-content' }}
            >
              +{remainingTags} more
            </div>
          )}
        </>
      );
    };

    return (
      <div key={item._id}>
        <div
          className="mx-1 mx-md-3 mx-lg-3"
          onClick={() => goToSnap(item)}
          style={{ cursor: 'pointer' }}
        >
          <div className="position-relative">
            <div className={`${Style.overlay}`}>
              {status && (
                <p
                  className="text-primary position-absolute rounded-3 fs-12"
                  style={{
                    backgroundColor: status.bgColor,
                    borderRadius: '4px',
                    padding: '3px 4px',
                    lineHeight: '150%',
                  }}
                >
                  {get(status, 'text')}
                </p>
              )}
            </div>

            <div className={`${Style.carouselCaption}`}>
              {get(item, 'snapshot') && (
                <h5 className={`${Style.titleText} text-light`}>
                  {get(item, 'project.cover.title')}
                </h5>
              )}
              <p className="p3 text-light">
                by {get(item, 'projectCreator.username')}
              </p>
            </div>
            <div className="position-relative">
              <img
                className={`${Style.coverDp}`}
                src={coverUrl}
                width={100}
                height={100}
                alt="cover"
                style={{ backgroundColor: '#0000' }}
              />
            </div>
          </div>

          <div className={`${Style.boxContainer}`}>
            {basicInfo && (
              <div className="row m-0">
                <p className={`p3 ${Style.logText}`}>
                  {get(basicInfo, 'logLine')
                    ? truncate(get(basicInfo, 'logLine'), { length: 250 })
                    : 'No log line'}
                </p>

                <div className="d-flex flex-wrap" style={{ gap: '16px' }}>
                  {projectTags.length > 0 ? (
                    renderTags(projectTags)
                  ) : (
                    <div
                      className={`${Style.badgePill}`}
                      style={{ width: 'fit-content' }}
                    >
                      No tags
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  });

  return (
    <>
      <div
        className={`d-flex flex-row ${
          isDashboardView ? 'justify-content-between' : 'justify-content-center'
        } mb-4 mt-5 align-items-center px-0 px-md-3 px-lg-3`}
      >
        {!isDashboardView && (
          <div style={{ position: 'absolute', left: '16px' }}>
            <InlineSvg
              height={24}
              width={24}
              src="/assets/svg/backArrow.svg"
              onClick={() =>
                setSlateList(['awaiting', 'interested', 'rejected'])
              }
              style={{ cursor: 'pointer' }}
            />
          </div>
        )}

        {/* Render section heading once with conditional classes */}
        <h2
          className={`text-primary m-0 fs-16 fs-md-20 fs-lg-20 ${
            isDashboardView ? 'd-flex align-items-center' : 'text-center w-100'
          }`}
        >
          {sectionHeading}
        </h2>

        {/* View All button only for dashboard view */}
        {isDashboardView && showSlates.length > 3 && (
          <div className="d-flex m-0">
            <button
              className={`${Style.viewListBtn} d-none d-md-block d-lg-block`}
              onClick={() => setSlateList([sectionId])}
            >
              View All
            </button>
          </div>
        )}
      </div>

      {slateListLength > 0 ? (
        isDashboardView ? (
          <CustomCarousel
            renderItems={showSlates}
            sectionHeading={sectionHeading}
            noProjectsMsg={noProjectsMsg}
          />
        ) : (
          <div className={`${Style.gridContainer}`}>{showSlates}</div>
        )
      ) : (
        <p className="text-center text-primary">{noProjectsMsg}</p>
      )}
      {isDashboardView && showSlates.length > 3 && (
        <div className="d-flex m-3 justify-content-center">
          <button
            className={`${Style.viewListBtn} d-block d-md-none d-lg-none`}
            onClick={() => setSlateList([sectionId])}
          >
            View All
          </button>
        </div>
      )}
    </>
  );
};

SlateList.propTypes = {
  sectionHeading: PropTypes.string.isRequired,
  noProjectsMsg: PropTypes.string.isRequired,
  setSlateList: PropTypes.func.isRequired,
  // Remove slateList prop, add calloutSubmissions and calloutId
  calloutSubmissions: PropTypes.object,
  calloutId: PropTypes.string,
  isDashboardView: PropTypes.bool.isRequired,
};

export default SlateList;
