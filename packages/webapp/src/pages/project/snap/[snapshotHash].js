/* eslint-disable @next/next/no-img-element */
import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import { withRouter } from 'next/router';
import Head from 'next/head';
import { withTranslation } from 'react-i18next';
import { get, isEmpty, size } from 'lodash';
import Loader from 'sharedComponents/loader';
import PropTypes from 'prop-types';
import {
  setLoadingStatus,
  setImageData,
  fetchProjectDetailsByHashID,
  toggleTheme,
  setCreatorTags,
  createFeedback,
  fetchDmProjects,
} from 'reducer/project';
import {
  setSnapRedirect,
  setSnapJourney,
  logout,
  setCachedRoute,
} from 'reducer/auth';
import { updateSlateStatus } from 'reducer/callout';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import Style from './styles/snapshot.module.scss';
import Cover from '../overview/rightSplitComponents/cover/cover';
import BasicInfo from '../overview/rightSplitComponents/basicInfo/basicInfo';
import CreativeTeam from '../overview/rightSplitComponents/creativeTeam';
import CastMembers from '../overview/rightSplitComponents/castMembers';
import Description from '../overview/rightSplitComponents/description/description';
import ProjectArtwork from '../overview/rightSplitComponents/projectArtwork';
import Video from '../overview/rightSplitComponents/video';
import CompareProject from '../overview/rightSplitComponents/compareProjects';
import FinancePlans from '../overview/rightSplitComponents/financePlans';
import Budget from '../overview/rightSplitComponents/budget';
import SalesEstimate from '../overview/rightSplitComponents/sales';
import OtherDocs from '../overview/rightSplitComponents/uploadFiles';
import Poster from '../overview/rightSplitComponents/poster';
// import SnapAuth from './components/snapAuth';
import MobileFooter from './components/mobileFooter';
import BcsSnap from './components/bcsSnap';
import Navbar from './components/bottomNavBar';
import InlineSvg from 'sharedComponents/inline-svg';
import NewTemplateSnapshot from './components/NewTemplateSnapshot';

class ProjectSnap extends PureComponent {
  constructor() {
    super();
    this.cover = React.createRef();
    this.basicInfo = React.createRef();
    this.creativeTeam = React.createRef();
    this.projectDisc = React.createRef();
    this.videos = React.createRef();
    this.artWork = React.createRef();
    this.castMembers = React.createRef();
    this.share = React.createRef();
    this.comparableProject = React.createRef();
    this.financePlan = React.createRef();
    this.budget = React.createRef();
    this.salesEstimate = React.createRef();
    this.otherDocs = React.createRef();
    this.footer = React.createRef();
  }

  async componentDidMount() {
    const {
      token,
      fetchProjectDetailsByHashID,
      router,
      setSnapRedirect,
      userData,
      setSnapJourney,
      setCachedRoute,
      createFeedback,
      fetchDmProjects,
    } = this.props;

    // Cache current route if not logged in
    if (!token) {
      setCachedRoute(router.asPath);
    } else {
      setCachedRoute('');
    }

    // Extracting snapshotHash safely
    let actualSnapshotHash = router.query.snapshotHash;

    // Fallback if query is not available (e.g. in production SSR)
    if (!actualSnapshotHash && router.asPath.includes('hash=')) {
      const hashPart = router.asPath.split('hash=')[1];
      actualSnapshotHash = hashPart.split('&')[0].split('?')[0];
    }

    // If we have a snapshot hash, proceed with API call
    if (actualSnapshotHash) {
      this.props.router.events.off(
        'routeChangeComplete',
        this.handleRouteChangeComplete,
      );

      const userSnap = await fetchProjectDetailsByHashID(actualSnapshotHash);

      if (isEmpty(userSnap)) {
        router.push('/projects');
      } else {
        const payloadData = {
          action: 'view',
          projectSnapId: get(userSnap, '_id'),
          user: {
            userId: get(userData, '_id'),
            email: get(userData, 'email'),
            name: get(userData, 'profile.name.fullName', ''),
            profileImage: get(userData, 'profile.profileImage', ''),
          },
        };

        createFeedback(payloadData);
        fetchDmProjects();
      }
    } else {
      // If snapshotHash was not found yet, wait for route to change
      this.handleRouteChangeComplete = () => {
        if (router.query.snapshotHash) {
          router.events.off(
            'routeChangeComplete',
            this.handleRouteChangeComplete,
          );
        }
      };

      router.events.on('routeChangeComplete', this.handleRouteChangeComplete);
    }

    // Handle redirect flag based on login state
    if (isEmpty(userData)) {
      setSnapRedirect(router.asPath);
    } else {
      setSnapRedirect(false);
      setSnapJourney(null);
    }
  }

  componentWillUnmount() {
    // Clean up event listener when the component unmounts
    this.props.router.events.off(
      'routeChangeComplete',
      this.handleRouteChangeComplete,
    );
  }
  render() {
    const {
      t,
      isLoading,
      setImageData,
      snapshotData,
      router,
      userData,
      // setSnapJourney,
      setCreatorTags,
      logout,
      createFeedback,
      feedback,
      fetchDmProjects,
      updateSlateStatus,
      token,
    } = this.props;

    let date = '';
    let time = '';
    let newTime = '';
    let snapHashData = {};
    if (snapshotData) {
      snapHashData = JSON.parse(snapshotData.body);
      date = snapshotData.createdAt.split('T')[0];
      time = snapshotData.createdAt.split('T')[1];
      newTime = time.split('.')[0];
    }

    console.log('router.query.status', router.query.status);
    console.log('full router.query', router.query);
    let status = null;
    if (get(router.query, 'status', null)) {
      status = router.query.status;
    }
    console.log('extracted status:', status);
    const fullContainerStyle = 'col-12 p-0';
    const footerBgImage = Style.backGroundImageFooter;

    const coverStatus = get(snapHashData, 'cover');
    const logLineStatus = get(snapHashData, 'basicInfo');
    const creativeTeamStatus = get(snapHashData, 'creativeTeam.length');
    const castMemberStatus = get(snapHashData, 'castMembers.length');
    const descriptionStatus = get(snapHashData, 'projectDisc');
    const artWorkStatus = get(snapHashData, 'artWork.length');
    const videoStatus = get(snapHashData, 'videos.length');
    const financeStatus = get(snapHashData, 'financePlan.length');
    const compareStatus = get(snapHashData, 'comparableProject.length');
    const salesEstimateStatus = get(snapHashData, 'salesEstimateFile.length');
    const otherDocsStatus = get(snapHashData, 'otherDocs.length');
    const theme = get(snapHashData, 'theme');
    const images = get(snapHashData, 'artWork');
    const coverDp = get(snapHashData, 'cover.coverPic', null);
    const createrInfo = get(snapHashData, 'contactDetails');
    const projectPoster = get(snapHashData, 'filmPosters', []);
    const snapId = get(snapshotData, '_id');
    const snapHash = get(snapshotData, 'hash');
    const projectId = get(snapshotData, 'projectId');

    const budget = get(snapHashData, 'budget');
    let totalBudget = 0;
    budget &&
      budget.map((item) => {
        totalBudget = totalBudget + item.amount;
      });

    // Description snap data status
    let visionDataStatus = false;
    let synopsisDataStatus = false;
    let scriptDataStatus = false;
    let treatmentDataStatus = false;
    if (descriptionStatus) {
      if (descriptionStatus.synopsis) {
        synopsisDataStatus = true;
      } else {
        synopsisDataStatus = false;
      }
      if (descriptionStatus.creativeVision) {
        visionDataStatus = true;
      } else {
        visionDataStatus = false;
      }
      if (descriptionStatus.script) {
        scriptDataStatus = true;
      } else {
        scriptDataStatus = false;
      }
      if (descriptionStatus.treatment) {
        treatmentDataStatus = true;
      } else {
        treatmentDataStatus = false;
      }
    }
    // Basic info snap data status
    let logLineTextDataStatus = false;
    let logLineProjectStatus = false;
    let logLineTagsStatus = false;
    let formatStatus = false;
    let genreStatus = false;
    let statusFieldStatus = false;
    let runningTimeStatus = false;
    let settingStatus = false;

    const createrTagsList = get(snapHashData, 'basicInfo.tags');

    if (logLineStatus) {
      if (logLineStatus.logLine) {
        logLineTextDataStatus = true;
      } else {
        logLineTextDataStatus = false;
      }
      if (logLineStatus.projectStatus && logLineStatus.projectStatus !== '') {
        logLineProjectStatus = true;
      } else {
        logLineProjectStatus = false;
      }
      if (size(createrTagsList)) {
        logLineTagsStatus = true;
      } else {
        logLineTagsStatus = false;
      }
      if (logLineStatus.format && logLineStatus.format !== 'Select') {
        formatStatus = true;
      } else {
        formatStatus = false;
      }
      if (logLineStatus.genre && logLineStatus.genre !== 'Select') {
        genreStatus = true;
      } else {
        genreStatus = false;
      }
      if (logLineStatus.status && logLineStatus.status !== 'Select') {
        statusFieldStatus = true;
      } else {
        statusFieldStatus = false;
      }
      if (logLineStatus.runningTime && logLineStatus.runningTime !== 'Select') {
        runningTimeStatus = true;
      } else {
        runningTimeStatus = false;
      }
      if (logLineStatus.setting && logLineStatus.setting !== 'Select') {
        settingStatus = true;
      } else {
        settingStatus = false;
      }
    }
    return (
      <div
        className="container-fluid themeContainer"
        style={{ padding: '0px' }}
      >
        <div className="col fixed-top p-0">
          <Head>
            <title> Project Preview </title>
            <link rel="shortcut icon" href="/favicon.ico" />
          </Head>
        </div>

        {isLoading ? (
          <Loader />
        ) : theme === 'bcsTemplate' ||
          theme === 'bcsTemplate2' ||
          theme === 'bcsTemplate3' ||
          theme === 'newTemplate' ? (
          <div
            className={`col-12 p-0 ${
              theme !== 'newTemplate'
                ? `${Style.bgContainer} ${coverDp ? '' : 'bg-primary'}`
                : ''
            }`}
            style={
              coverDp && theme !== 'newTemplate'
                ? {
                    background: `url(${coverDp})`,
                    backgroundRepeat: 'no-repeat',
                    objectFit: 'cover',
                    backgroundSize: '100%',
                  }
                : {}
            }
          >
            {/* {!isEmpty(userData) || guestUserStatus === 'd2b297243f27' ? (
              <> */}
            <div className={`w-100 d-flex align-items-center`}>
              <p className={`${Style.headerContainer} m-0`}>
                Snapshot taken by SMASH on {date} at {newTime}.
              </p>
            </div>
            {theme === 'newTemplate' ? (
              <NewTemplateSnapshot
                router={router}
                projectPreviewData={snapHashData}
                createFeedback={createFeedback}
                createrInfo={createrInfo}
                snapId={snapId}
                userData={userData}
                feedback={feedback}
                fetchDmProjects={fetchDmProjects}
                footerStatus
                coverStatus={coverStatus}
                snapHash={snapHash}
                projectId={projectId}
                logout={logout}
                updateSlateStatus={updateSlateStatus}
                logLineStatus={logLineStatus}
                creativeTeamStatus={creativeTeamStatus}
                castMemberStatus={castMemberStatus}
                descriptionStatus={descriptionStatus}
                artWorkStatus={artWorkStatus}
                videoStatus={videoStatus}
                compareStatus={compareStatus}
                financeStatus={financeStatus}
                budgetStatus={totalBudget}
                salesEstimateStatus={salesEstimateStatus}
                otherDocsStatus={otherDocsStatus}
                projectPoster={projectPoster}
              />
            ) : (
              <BcsSnap
                t={t}
                router={router}
                coverData={snapHashData.cover}
                projectPreviewData={snapHashData}
                imageList={images}
                coverStatus={coverStatus}
                logLineStatus={logLineStatus}
                creativeTeamStatus={creativeTeamStatus}
                castMemberStatus={castMemberStatus}
                descriptionStatus={descriptionStatus}
                artWorkStatus={artWorkStatus}
                videoStatus={videoStatus}
                compareStatus={compareStatus}
                financeStatus={financeStatus}
                budgetStatus={totalBudget}
                salesEstimateStatus={salesEstimateStatus}
                otherDocsStatus={otherDocsStatus}
                logLineTextDataStatus={logLineTextDataStatus}
                logLineProjectStatus={logLineProjectStatus}
                logLineTagsStatus={logLineTagsStatus}
                formatStatus={formatStatus}
                genreStatus={genreStatus}
                statusFieldStatus={statusFieldStatus}
                runningTimeStatus={runningTimeStatus}
                settingStatus={settingStatus}
                visionDataStatus={visionDataStatus}
                synopsisDataStatus={synopsisDataStatus}
                treatmentDataStatus={treatmentDataStatus}
                scriptDataStatus={scriptDataStatus}
                snapHashData={snapHashData}
                logout={logout}
                createFeedback={createFeedback}
                createrInfo={createrInfo}
                snapId={snapId}
                userData={userData}
                feedback={feedback}
                fetchDmProjects={fetchDmProjects}
                snapHash={snapHash}
                projectId={projectId}
                projectPoster={projectPoster}
                updateSlateStatus={updateSlateStatus}
              />
            )}
            {/* </>
            ) : (
              <div className={`${Style.authRowContainer}`}>
                <SnapAuth
                  t={t}
                  coverData={snapHashData.cover}
                  setSnapJourney={setSnapJourney}
                  theme={theme}
                  snapHashData={snapHashData}
                />
              </div>
            )} */}
          </div>
        ) : (
          <div>
            <Navbar
              status={status}
              token={token}
              router={router}
              heightRef={this.footer}
              reduceHeight={1000}
              logout={logout}
              createFeedback={createFeedback}
              createrInfo={createrInfo}
              snapId={snapId}
              userData={userData}
              feedback={feedback}
              fetchDmProjects={fetchDmProjects}
              footerStatus
              projectId={projectId}
              coverStatus={coverStatus}
              snapHash={snapHash}
              updateSlateStatus={updateSlateStatus}
            />
            <div className="container">
              <div className="row m-0 m-sm-0">
                <div
                  ref={this.footer}
                  className={`${Style.shareBgContainer} ${fullContainerStyle}`}
                >
                  {/* {!isEmpty(userData) || guestUserStatus === 'd2b297243f27' ? (
                    <> */}
                  <div>
                    <div ref={this.cover} className="d-flex">
                      <div className={`${Style.headerSquareContainer}`} />
                      <div className={`w-100 d-flex align-items-center`}>
                        <p className={`${Style.headerContainer} m-0`}>
                          Snapshot taken by SMASH on {date} at {newTime}.
                        </p>
                      </div>
                    </div>
                    <div
                      className={`${Style.lockContainer} d-none d-sm-none d-md-flex flex-row-reverse mt-4`}
                    >
                      <InlineSvg
                        src="/assets/gif/smashLock.gif"
                        alt="profile"
                        className={`${Style.img}`}
                      />
                    </div>
                    <div className="m0Auto col-md-10 col-xxl-8">
                      {get(snapHashData, 'sectionsOnOff.cover', 'unLock') ===
                        'unLock' &&
                        coverStatus && (
                          <>
                            <div ref={this.cover} data-cy="coverId">
                              <Cover
                                coverData={snapHashData.cover}
                                snapStatus
                              />
                            </div>
                            <hr className={`${Style.hrBorder}`} />
                          </>
                        )}

                      {get(
                        snapHashData,
                        'sectionsOnOff.basicInfo',
                        'unLock',
                      ) === 'unLock' &&
                        logLineStatus && (
                          <>
                            <div ref={this.basicInfo} data-cy="basicId">
                              <BasicInfo
                                projectPreviewData={snapHashData}
                                setCreatorTags={setCreatorTags}
                                snapStatus
                                logLineTextDataStatus={logLineTextDataStatus}
                                logLineProjectStatus={logLineProjectStatus}
                                logLineTagsStatus={logLineTagsStatus}
                                formatStatus={formatStatus}
                                genreStatus={genreStatus}
                                statusFieldStatus={statusFieldStatus}
                                runningTimeStatus={runningTimeStatus}
                                settingStatus={settingStatus}
                                iconHide={true}
                              />
                            </div>
                            <hr className={`${Style.hrBorder}`} />
                          </>
                        )}

                      {get(
                        snapHashData,
                        'sectionsOnOff.creativeTeam',
                        'unLock',
                      ) === 'unLock' &&
                        creativeTeamStatus > 0 && (
                          <>
                            <div ref={this.creativeTeam}>
                              <CreativeTeam
                                projectPreviewData={snapHashData}
                                snapStatus
                                iconHide={true}
                              />
                            </div>
                            <hr className={`${Style.hrBorder}`} />
                          </>
                        )}

                      {get(
                        snapHashData,
                        'sectionsOnOff.castMembers',
                        'unLock',
                      ) === 'unLock' &&
                        castMemberStatus > 0 && (
                          <>
                            <div ref={this.castMembers}>
                              <CastMembers
                                projectPreviewData={snapHashData}
                                snapStatus
                                iconHide={true}
                              />
                            </div>
                            <hr className={`${Style.hrBorder}`} />
                          </>
                        )}

                      {get(
                        snapHashData,
                        'sectionsOnOff.projectDisc',
                        'unLock',
                      ) === 'unLock' &&
                        descriptionStatus && (
                          <>
                            <div
                              ref={this.projectDisc}
                              className={`${Style.centerAlign} row col-md-11`}
                            >
                              <Description
                                projectPreviewData={snapHashData}
                                snapStatus
                                visionDataStatus={visionDataStatus}
                                synopsisDataStatus={synopsisDataStatus}
                                treatmentDataStatus={treatmentDataStatus}
                                scriptDataStatus={scriptDataStatus}
                                iconHide={true}
                              />
                            </div>
                            <hr className={`${Style.hrBorder}`} />
                          </>
                        )}

                      {get(snapHashData, 'sectionsOnOff.poster', 'unLock') ===
                        'unLock' &&
                        projectPoster.length > 0 && (
                          <>
                            <div
                              ref={this.comparableProject}
                              className={`${Style.centerAlign}`}
                            >
                              <Poster
                                projectPreviewData={snapHashData}
                                snapStatus
                                t={t}
                                iconHide={true}
                              />
                            </div>
                            <hr className={`${Style.hrBorder}`} />
                          </>
                        )}

                      {get(snapHashData, 'sectionsOnOff.artWork', 'unLock') ===
                        'unLock' &&
                        artWorkStatus > 0 && (
                          <>
                            <div ref={this.artWork} className="row m-0">
                              <ProjectArtwork
                                setImageData={setImageData}
                                projectPreviewData={snapHashData}
                                imageList={images}
                                snapStatus
                                iconHide={true}
                              />
                            </div>
                            <hr className={`${Style.hrBorder}`} />
                          </>
                        )}

                      {get(snapHashData, 'sectionsOnOff.videos', 'unLock') ===
                        'unLock' &&
                        videoStatus > 0 && (
                          <>
                            <div ref={this.videos}>
                              <Video
                                t={t}
                                projectPreviewData={snapHashData}
                                snapStatus
                                iconHide={true}
                              />
                              <hr className={`${Style.hrBorder}`} />
                            </div>
                          </>
                        )}
                      {get(
                        snapHashData,
                        'sectionsOnOff.comparableProject',
                        'unLock',
                      ) === 'unLock' &&
                        compareStatus > 0 && (
                          <>
                            <div
                              ref={this.comparableProject}
                              className={`${Style.centerAlign}`}
                            >
                              <CompareProject
                                projectPreviewData={snapHashData}
                                snapStatus
                                t={t}
                                iconHide={true}
                              />
                            </div>
                            <hr className={`${Style.hrBorder}`} />
                          </>
                        )}

                      {get(
                        snapHashData,
                        'sectionsOnOff.financePlan',
                        'unLock',
                      ) === 'unLock' &&
                        financeStatus > 0 && (
                          <>
                            <div
                              ref={this.financePlan}
                              className={`${Style.centerAlign}`}
                            >
                              <FinancePlans
                                projectPreviewData={snapHashData}
                                snapStatus
                                t={t}
                                iconHide={true}
                              />
                            </div>
                            <hr className={`${Style.hrBorder}`} />
                          </>
                        )}
                      {get(snapHashData, 'sectionsOnOff.budget', 'unLock') ===
                        'unLock' &&
                        totalBudget > 0 && (
                          <>
                            <div
                              ref={this.budget}
                              className={`${Style.centerAlign}`}
                            >
                              <Budget
                                projectPreviewData={snapHashData}
                                snapStatus
                                t={t}
                                iconHide={true}
                              />
                            </div>
                            <hr className={`${Style.hrBorder}`} />
                          </>
                        )}
                      {get(
                        snapHashData,
                        'sectionsOnOff.uploadFileSection',
                        'unLock',
                      ) === 'unLock' &&
                        otherDocsStatus > 0 && (
                          <>
                            <div
                              ref={this.otherDocs}
                              className={`${Style.centerAlign} mt-4`}
                            >
                              <OtherDocs
                                projectPreviewData={snapHashData}
                                snapStatus
                                t={t}
                                iconHide={true}
                              />
                            </div>
                            <hr className={`${Style.hrBorder}`} />
                          </>
                        )}
                      {get(
                        snapHashData,
                        'sectionsOnOff.salesEstimateFile',
                        'unLock',
                      ) === 'unLock' &&
                        salesEstimateStatus > 0 && (
                          <>
                            <div
                              ref={this.salesEstimate}
                              className={`${Style.centerAlign} mt-4`}
                            >
                              <SalesEstimate
                                projectPreviewData={snapHashData}
                                snapStatus
                                t={t}
                                iconHide={true}
                              />
                            </div>
                            <hr className={`${Style.hrBorder}`} />
                          </>
                        )}
                    </div>
                  </div>
                  <>
                    <div className="d-block d-sm-block d-md-none">
                      <MobileFooter snapHashData={snapHashData} />
                    </div>
                    <div
                      ref={this.footer}
                      className={`${Style.footerContainer} ${footerBgImage} d-none d-sm-none d-md-flex`}
                    >
                      <div
                        className={`${Style.footerRow} col-lg-5 d-flex col-md-12`}
                      >
                        <div className="col-lg-3 text-right" />
                        <div className="col-md-12 col-lg-6">
                          <div className={`${Style.footerTitleText}`}>
                            {get(snapHashData, 'cover.title') &&
                              snapHashData.cover.title}
                          </div>
                          {get(snapHashData, 'regNo') && (
                            <div
                              className={`${Style.footerText}`}
                              data-cy="regNo"
                            >
                              Project identifier #{snapHashData.regNo}
                            </div>
                          )}
                        </div>
                      </div>
                      <div
                        className={`${Style.footerTitleText} col-lg-4 col-md-12`}
                        id="fullName"
                        data-cy="fullName"
                      >
                        By{' '}
                        {get(snapHashData, 'contactDetails.fullName') &&
                          snapHashData.contactDetails.fullName}
                        {get(snapHashData, 'contactDetails.email') && (
                          <div
                            className={`${Style.footerText} font-weight-normal`}
                            id="email"
                            data-cy="email"
                          >
                            {snapHashData.contactDetails.email}
                          </div>
                        )}
                      </div>
                      <div
                        className={`${Style.footerImage} col-lg-3 col-md-12 mt-4`}
                      >
                        <InlineSvg
                          src="/assets/svg/smashlogowhitenew.svg"
                          width="160px"
                          className={`${Style.logo}`}
                          alt=""
                        />
                      </div>
                    </div>
                  </>
                  {/* </>
                  ) : (
                    <div className={`${Style.authRowContainer}`}>
                      <SnapAuth
                        t={t}
                        coverData={snapHashData.cover}
                        setSnapJourney={setSnapJourney}
                        theme={theme}
                        snapHashData={snapHashData}
                      />
                    </div>
                  )} */}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  isLoading: state.project.isLoading,
  coverData: state.project.coverData,
  snapshotData: state.project.snapshotData,
  userData: state.auth.userData,
  feedback: state.project.dmProjectsList,
  token: state.auth.token,
});

const mapDispatchToProps = (dispatch) => {
  return {
    setLoadingStatus: (payload) => dispatch(setLoadingStatus(payload)),
    setImageData: (payload) => dispatch(setImageData(payload)),
    fetchProjectDetailsByHashID: (payload) =>
      dispatch(fetchProjectDetailsByHashID(payload)),
    toggleTheme: (value, id) => dispatch(toggleTheme(value, id)),
    setSnapRedirect: (payload) => dispatch(setSnapRedirect(payload)),
    setSnapJourney: (payload) => dispatch(setSnapJourney(payload)),
    setCreatorTags: (payload) => dispatch(setCreatorTags(payload)),
    logout: (payload) => dispatch(logout(payload)),
    createFeedback: (payload) => dispatch(createFeedback(payload)),
    fetchDmProjects: () => dispatch(fetchDmProjects()),
    updateSlateStatus: (id, data) => dispatch(updateSlateStatus(id, data)),
    setCachedRoute: (route) => dispatch(setCachedRoute(route)),
  };
};

ProjectSnap.propTypes = {
  isLoading: PropTypes.bool.isRequired,
  router: PropTypes.object.isRequired,
  fetchProjectDetailsByHashID: PropTypes.func.isRequired,
  t: PropTypes.func.isRequired,
  setImageData: PropTypes.func.isRequired,
  snapshotData: PropTypes.object.isRequired,
  setSnapRedirect: PropTypes.func.isRequired,
  userData: PropTypes.object.isRequired,
  setSnapJourney: PropTypes.func.isRequired,
  setCreatorTags: PropTypes.func.isRequired,
  logout: PropTypes.func.isRequired,
  createFeedback: PropTypes.func.isRequired,
  feedback: PropTypes.string.isRequired,
  fetchDmProjects: PropTypes.func.isRequired,
  updateSlateStatus: PropTypes.func.isRequired,
};

export async function getStaticPaths() {
  return {
    paths: ['/project/snap/snapshotHash'],
    fallback: true,
  };
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale)),
    },
  };
}

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withRouter(withTranslation('common')(ProjectSnap)));
