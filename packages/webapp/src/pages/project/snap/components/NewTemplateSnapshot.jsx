/* eslint-disable @next/next/no-img-element */
import React, { useRef } from 'react';
import { get } from 'lodash';
import Cover from 'sharedComponents/CascadeTemplate/Cover/cover';
import QRComponent from 'sharedComponents/qrCode/qrCode';
import MobileFooter from './mobileFooter';
import Navbar from './bottomNavBar';
import Style from '../styles/snapshot.module.scss';
import InlineSvg from 'sharedComponents/inline-svg';
import BasicInfo from 'sharedComponents/CascadeTemplate/BasicInfo/basicInfo';
import CreativeTeam from 'sharedComponents/CascadeTemplate/CreativeTeam/creativeTeam';
import Description from 'sharedComponents/CascadeTemplate/Description/description';
import CastMember from 'sharedComponents/CascadeTemplate/CastMember/castMember';
import ProjectPoster from 'sharedComponents/CascadeTemplate/Poster/poster';
import MoodBoard from 'sharedComponents/CascadeTemplate/MoodBoard/moodBoard';
import ProjectVideo from 'sharedComponents/CascadeTemplate/ProjectVideo/projectVideo';
import CompareProjects from 'sharedComponents/CascadeTemplate/ComparableProjects/comparableProject';
import Funding from 'sharedComponents/CascadeTemplate/Finance/funding';
import Budget from 'sharedComponents/CascadeTemplate/Budget/budget';
import SalesEstimate from 'sharedComponents/CascadeTemplate/SalesEstimate/salesEstimate';
import OtherDocuments from 'sharedComponents/CascadeTemplate/OtherDocuments/otherDocuments';

const NewTemplateSnapshot = ({
  t,
  coverStatus,
  router,
  status,
  token,
  logout,
  createFeedback,
  createrInfo,
  snapId,
  userData,
  feedback,
  fetchDmProjects,
  snapHash,
  projectId,
  updateSlateStatus,
  projectPreviewData,
  snapHashData,
  logLineStatus,
  creativeTeamStatus,
  descriptionStatus,
  castMemberStatus,
  artWorkStatus,
  videoStatus,
  compareStatus,
  financeStatus,
  budgetStatus,
  salesEstimateStatus,
  otherDocsStatus,
  projectPoster,
}) => {
  const footer = useRef(null);
  const regNo = get(projectPreviewData, 'regNo');
  const coverImage = get(projectPreviewData, 'cover.coverPic', '');
  const title = get(projectPreviewData, 'cover.title', '');
  const producer = get(projectPreviewData, 'cover.producer', '');
  const director = get(projectPreviewData, 'cover.director', '');
  const writer = get(projectPreviewData, 'cover.writer', '');
  const logline = get(projectPreviewData, 'basicInfo.logLine', '');
  const format = get(projectPreviewData, 'basicInfo.format', '');
  const projectStatus = get(projectPreviewData, 'basicInfo.projectStatus', '');
  const genres = get(projectPreviewData, 'basicInfo.genre', '');
  const settings = get(projectPreviewData, 'basicInfo.setting', '');
  const runtimes = get(projectPreviewData, 'basicInfo.runningTime', '');
  const status = get(projectPreviewData, 'basicInfo.status', '');
  const projectTags = get(projectPreviewData, 'basicInfo.tags', []);
  const creativeTeamData = get(projectPreviewData, 'creativeTeam', []);
  const synopsis = get(projectPreviewData, 'projectDisc.synopsis', '');
  const creativeVision = get(projectPreviewData, 'projectDisc.creativeVision');
  const treatment = get(projectPreviewData, 'projectDisc.treatment', {});
  const castMember = get(projectPreviewData, 'castMembers', []);
  const script = get(projectPreviewData, 'projectDisc.script', {});
  const posters = get(projectPreviewData, 'filmPosters', []);
  const moodBoard = get(projectPreviewData, 'artWork', []);
  const videoUrls = get(projectPreviewData, 'videos', []);
  const comparableProjects = get(projectPreviewData, 'comparableProject', []);
  const sales = get(projectPreviewData, 'salesEstimateFile');
  const docs = get(projectPreviewData, 'otherDocs');
  const fullName = get(projectPreviewData, 'contactDetails.fullName');
  const email = get(projectPreviewData, 'contactDetails.email');
  const financePlan = get(projectPreviewData, 'financePlan', []);
  const showStatus =
    get(projectPreviewData, 'unestimatedBudget.showStatus') === 'unestimated'
      ? 'unestimated'
      : 'estimated';

  //calculate total funding
  let totalFund = 0;
  financePlan.forEach((item) => {
    if (item.amounts) {
      totalFund += item.amounts;
    }
  });

  const isSectionUnlocked =
    get(projectPreviewData, 'sectionsOnOff.cover', 'unLock') === 'unLock';

  const hasBasicInfo =
    logline ||
    format ||
    genres ||
    settings ||
    runtimes ||
    status ||
    projectStatus ||
    projectTags.length > 0;

  return (
    <>
      <div ref={footer} id="template">
        {isSectionUnlocked && (
          <>
            {get(projectPreviewData, 'sectionsOnOff.cover', 'unLock') ===
              'unLock' &&
              coverStatus &&
              coverImage && (
                <Cover
                  t={t}
                  coverImage={coverImage}
                  title={title}
                  producer={producer}
                  director={director}
                  writer={writer}
                  snapStatus={true}
                />
              )}
            {get(projectPreviewData, 'sectionsOnOff.basicInfo', 'unLock') ===
              'unLock' &&
              logLineStatus &&
              hasBasicInfo && (
                <BasicInfo
                  logline={logline}
                  format={format}
                  genre={genres}
                  setting={settings}
                  runtime={runtimes}
                  status={status}
                  projectHighlights={projectStatus}
                  projectTags={projectTags}
                  snapStatus={true}
                />
              )}
            {get(projectPreviewData, 'sectionsOnOff.creativeTeam', 'unLock') ===
              'unLock' &&
              creativeTeamStatus > 0 && (
                <CreativeTeam profiles={creativeTeamData} />
              )}
            {get(projectPreviewData, 'sectionsOnOff.projectDisc', 'unLock') ===
              'unLock' &&
              descriptionStatus && (
                <Description
                  synopsis={synopsis}
                  creativeVision={creativeVision}
                  script={script}
                  treatment={treatment}
                />
              )}
            {get(projectPreviewData, 'sectionsOnOff.castMembers', 'unLock') ===
              'unLock' &&
              castMemberStatus > 0 && <CastMember castMembers={castMember} />}
            {get(projectPreviewData, 'sectionsOnOff.poster', 'unLock') ===
              'unLock' &&
              projectPoster.length > 0 && <ProjectPoster posters={posters} />}
            {get(projectPreviewData, 'sectionsOnOff.artWork', 'unLock') ===
              'unLock' &&
              artWorkStatus > 0 && <MoodBoard moodPosters={moodBoard} />}
            {get(projectPreviewData, 'sectionsOnOff.videos', 'unLock') ===
              'unLock' &&
              videoStatus > 0 && <ProjectVideo videoUrls={videoUrls} />}
            {get(
              projectPreviewData,
              'sectionsOnOff.comparableProject',
              'unLock',
            ) === 'unLock' &&
              compareStatus > 0 && (
                <CompareProjects compareProjects={comparableProjects} />
              )}
            {get(projectPreviewData, 'sectionsOnOff.financePlan', 'unLock') ===
              'unLock' &&
              financeStatus > 0 && (
                <Funding
                  totalFunds={get(projectPreviewData, 'totalFinanceSum', 0)}
                  uniqueFundingList={get(projectPreviewData, 'financePlan', {})}
                  totalFund={totalFund}
                />
              )}
            {get(projectPreviewData, 'sectionsOnOff.budget', 'unLock') ===
              'unLock' &&
              budgetStatus > 0 && (
                <Budget
                  projectPreviewData={projectPreviewData}
                  totalFunds={get(projectPreviewData, 'totalBudget', 0)}
                  uniqueFundingList={get(projectPreviewData, 'budget', {})}
                  financing={get(projectPreviewData, 'financePlan', [])}
                  totalFund={totalFund}
                  showEditBtn={false}
                  showStatus={showStatus}
                  availableFunding={totalFund}
                  showUnestimatedAmount={get(
                    projectPreviewData,
                    'unestimatedBudget.amount',
                    0,
                  )}
                  projectPreview={projectPreviewData}
                />
              )}
            {get(
              projectPreviewData,
              'sectionsOnOff.salesEstimateFile',
              'unLock',
            ) === 'unLock' &&
              salesEstimateStatus > 0 && <SalesEstimate sales={sales} />}
            {get(
              projectPreviewData,
              'sectionsOnOff.uploadFileSection',
              'unLock',
            ) === 'unLock' &&
              otherDocsStatus > 0 && <OtherDocuments docs={docs} />}
          </>
        )}
      </div>
      {snapId && (
        <div className="mt-4">
          <Navbar
            status={status}
            token={token}
            router={router}
            heightRef={footer}
            reduceHeight={100}
            logout={logout}
            createFeedback={createFeedback}
            fetchDmProjects={fetchDmProjects}
            createrInfo={createrInfo}
            snapId={snapId}
            userData={userData}
            feedback={feedback}
            footerStatus
            coverStatus={coverStatus}
            snapHash={snapHash}
            projectId={projectId}
            updateSlateStatus={updateSlateStatus}
          />
        </div>
      )}
      <>
        <div className="d-block d-sm-block d-md-none">
          <MobileFooter snapHashData={snapHashData} />
        </div>
        <div
          className={`${Style.footerContainer} ${Style.backGroundImageFooter} d-none d-sm-flex d-md-flex`}
        >
          <div className="row w-100 justify-content-between">
            {/* QR Component */}
            <div className="col-12 col-lg-3 col-sm-6 text-center mt-4">
              <QRComponent
                url={`${process.env.SnapBaseUrl}project/snap/${router.query.snapshotHash}`}
              />
            </div>

            {/* Project Title and Registration Number */}
            <div className="col-12 col-lg-3 col-sm-6 text-center mt-4">
              <div className={`${Style.footerTitleText} mb-3`}>{title}</div>
              {regNo && (
                <div className={`${Style.footerText}`} data-cy="regNo">
                  Project identifier #{regNo}
                </div>
              )}
            </div>

            {/* Project Creator Information */}
            <div className="col-12 col-lg-3 col-sm-6 text-center mt-4">
              <div className={`${Style.footerHeadingText}`}>
                Project creator
              </div>
              {fullName && (
                <div className={`${Style.footerText}`} id="fullName">
                  {fullName}
                </div>
              )}
              {email && (
                <div
                  className={`${Style.footerText}`}
                  id="email"
                  data-cy="email"
                >
                  {email}
                </div>
              )}
            </div>

            {/* Logo */}
            <div className="col-12 col-lg-3 col-sm-6 text-center mt-4">
              <InlineSvg
                src="/assets/svg/smashlogowhitenew.svg"
                width="160px"
                className={`${Style.logo}`}
                alt="Project Logo"
              />
            </div>
          </div>
        </div>
      </>
    </>
  );
};

export default NewTemplateSnapshot;
