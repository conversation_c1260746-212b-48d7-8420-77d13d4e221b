/* eslint-disable object-shorthand */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable  no-nested-ternary */
/* eslint-disable  prefer-template */

import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { get, isEmpty, snakeCase } from 'lodash';
import Router from 'next/router';
import Style from '../styles/bottomNavBar.module.scss';
import Profile from './profile';
import style from 'sharedComponents/styles/dashboardHeader.module.scss';
import DropDownComp from 'sharedComponents/dropDown/dropdown';
import InlineSvg from 'sharedComponents/inline-svg';

const Navbar = (props) => {
  const [show, setShow] = useState(true);
  const [feedback, setFeedback] = useState('');

  const [isBottom, setBottomStatus] = useState(false);
  const [isHover, setHoverStatus] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const [guestUserStatus, setGuestUserStatus] = useState(false);
  const { notes, id, slateId } = Router.query;
  let oldScrolly = 0;
  const controlNavbar = () => {
    if (window.scrollY > 100) {
      setHoverStatus(true);
      setShow(true);
      if (window.scrollY >= props.heightRef.current.offsetHeight) {
        oldScrolly = window.scrollY;
        setShow(true);
        setHoverStatus(true);
        setBottomStatus(false);
      } else if (oldScrolly >= window.scrollY) {
        setShow(true);
        setBottomStatus(true);
        setHoverStatus(false);
        oldScrolly = window.scrollY;
      } else {
        oldScrolly = window.scrollY;
        setShow(true);
        setHoverStatus(true);
        setBottomStatus(false);
      }
    } else {
      oldScrolly = window.scrollY;
      setShow(true);
      setBottomStatus(false);
    }
  };

  console.log('==================== Navbar props.status:', props.status);
  console.log(
    '==================== Navbar props.submissionId:',
    props.submissionId,
  );
  console.log(
    '==================== Navbar props.getSlateStatus:',
    typeof props.getSlateStatus,
  );
  console.log('==================== All Navbar props:', Object.keys(props));
  console.log('==================== Current feedback state:', feedback);

  /*** method to logout user from app ****/
  const logoutRedirect = () => {
    const { logout } = props;
    logout(props);
  };

  const toggleDropdown = () => {
    setShowDropdown(!showDropdown);
  };

  const hideDropdown = () => {
    setShowDropdown(false);
  };

  const setFeedbacks = async (feedback1) => {
    const {
      createFeedback,
      snapId,
      userData,
      updateSlateStatus,
      submissionId,
      getSlateStatus,
    } = props;
    console.log('fmlkdsmflkmlkdsf', submissionId);
    if (submissionId) {
      const statusMapping = {
        letsTalk: 'LETS_TALK',
        notInterested: 'NOT_INTERESTED',
        tracking: 'TRACKING',
        view: 'AWAITING_FEEDBACK',
      };

      const result = getSlateStatus(submissionId, {
        status: statusMapping[feedback1],
      });
      console.log('==================== result:', result);
    } else {
      const data = {
        action: feedback1,
        projectSnapId: snapId,
        user: {
          userId: userData._id,
          email: userData.email,
          name: userData.profile.name.fullName,
          profileImage: userData.profile.profileImage,
        },
      };
      createFeedback(data);
      setFeedback(feedback1);

      if (id && slateId) {
        updateSlateStatus(id, { id: slateId, status: snakeCase(feedback1) });
      }
    }
  };

  useEffect(() => {
    const { userData, createrInfo, router, status } = props;
    const isguest = get(router, 'query.e47b', false);
    setGuestUserStatus(isguest);

    const isOwner = get(userData, '_id') === get(createrInfo, 'userId');

    //to hide the navbar if the user is the owner of the project
    if (isOwner) {
      setShow(false);
      return;
    }

    // Status mapping from props.status to feedback state
    const statusMapping = {
      LETS_TALK: 'letsTalk',
      NOT_INTERESTED: 'notInterested',
      TRACKING: 'tracking',
      AWAITING_FEEDBACK: 'view',
    };

    // If status prop is provided, use it; otherwise use existing feedback logic
    if (status && statusMapping[status]) {
      setFeedback(statusMapping[status]);
    } else {
      // Existing feedback logic
      props.feedback.forEach((item) => {
        const email = props.userData.email;
        const matchedActivity = item.activities.find(
          (snapData) =>
            snapData.user.email === email && item._id === props.snapId,
        );

        if (matchedActivity) {
          setFeedback(matchedActivity.action);
        }
      });
    }

    const projectOwnerInfo = {};
    if (createrInfo) {
      projectOwnerInfo.projectOwnerId = createrInfo.userId;
      projectOwnerInfo.name = createrInfo.fullName;
      projectOwnerInfo.email = createrInfo.email;
    }

    window.addEventListener('scroll', controlNavbar);
    return () => {
      window.removeEventListener('scroll', controlNavbar);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [props.feedback, props.status]);

  const dropdownOptions = [
    {
      icon: '',
      uploadControl: false,
      handler: () => Router.push('/projects'),
      value: 'MY PROJECTS',
    },
    {
      icon: '',
      uploadControl: false,
      handler: () => Router.push('/callouts'),
      value: 'CALL OUTS',
    },
    {
      icon: '',
      uploadControl: false,
      handler: () => Router.push('/discovery'),
      value: 'DISCOVERY',
    },
    {
      icon: '',
      uploadControl: false,
      handler: () => Router.push('/marketPlace'),
      value: 'MARKET PLACE',
    },
    {
      icon: '',
      uploadControl: false,
      handler: () => Router.push('/discoverer/dashboard'),
      value: 'WATCH LIST',
    },
    {
      icon: '',
      uploadControl: false,
      handler: () => Router.push('/myaccount'),
      value: 'MY PROFILE',
    },
    {
      icon: '',
      uploadControl: false,
      handler: logoutRedirect,
      value: 'SIGN OUT',
    },
  ];
  return (
    <>
      {show && (
        <div
          className={`col-12 p-0 justify-content-center ${Style.nav} ${
            show
              ? isBottom
                ? Style.bottomNav
                : isHover
                  ? Style.hoverNav
                  : Style.navTop
              : ''
          }`}
        >
          <div
            className={`w-100 ${Style.feedbackHover} ${isHover ? Style.showHover : ''}`}
          >
            {(!isEmpty(props.userData) ||
              guestUserStatus === 'd2b297243f27') && (
              <div
                className="row align-items-center justify-content-center"
                style={{
                  height: '80px',
                }}
              >
                <div
                  className={`col-3 col-md-5 px-1 px-md-5 px-lg-5align-items-center justify-content-center`}
                >
                  {showDropdown ? (
                    <InlineSvg
                      src="/assets/svg/crossIcon.svg"
                      onClick={hideDropdown}
                      width="48px"
                      height="48px"
                    />
                  ) : (
                    <Profile
                      logout={props.logout}
                      userData={props.userData}
                      source="callout"
                      onClick={toggleDropdown}
                      style={{
                        width: '64px', // Adjust profile width
                        height: '64px', // Adjust profile height
                      }}
                    />
                  )}
                </div>
                <div className="col-8 col-md-7">
                  <div className={`${Style.feedbackHeaders}`}>
                    LEAVE FEEDBACK
                  </div>
                </div>
              </div>
            )}

            <div className="row">
              <div className="col-12 d-flex justify-content-center mt-3">
                {!isEmpty(props.userData) ||
                guestUserStatus === 'd2b297243f27' ? (
                  <div className="mt-3">
                    <button
                      type="button"
                      className={`p-0 ${Style.btns} ${Style.talkBtn} ${
                        feedback === 'letsTalk' && Style.talkBg
                      } btn btn-outline-primary mx-12 mr-1 ml-sm-1 mr-sm-1 ml-md-3 mr-md-3 mb-4`}
                      onClick={() => setFeedbacks('letsTalk')}
                    >
                      let’s talk
                    </button>
                    <button
                      type="button"
                      onClick={() => setFeedbacks('tracking')}
                      className={`p-0 ${Style.btns} ${Style.trackingBtn} ${
                        feedback === 'tracking' && Style.trackingBg
                      } btn btn-outline-primary mx-12 mr-1 ml-sm-1 mr-sm-1 ml-md-3 mr-md-3 mb-4`}
                    >
                      tracking
                    </button>
                    <button
                      type="button"
                      onClick={() => setFeedbacks('notInterested')}
                      className={`p-0 ${Style.btns} ${Style.notForMeBtn} ${
                        feedback === 'notInterested' && Style.notForMeBg
                      } btn btn-outline-primary mx-12 mr-1 ml-sm-1 mr-sm-1 ml-md-3 mr-md-3 mb-4`}
                    >
                      thanks but not for me
                    </button>
                  </div>
                ) : (
                  <>
                    <button
                      type="button"
                      className={`p-0 ${Style.btns} ${Style.talkBtn} ${
                        feedback === 'letsTalk' && Style.talkBg
                      } btn btn-outline-primary mx-12 mr-1 ml-sm-1 mr-sm-1 ml-md-3 mr-md-3 mb-4`}
                      onClick={() => Router.push('/')}
                    >
                      Login
                    </button>
                    <button
                      type="button"
                      onClick={() => Router.push('/signUp')}
                      className={`p-0 ${Style.btns} ${Style.trackingBtn} ${
                        feedback === 'tracking' && Style.trackingBg
                      } btn btn-outline-primary mx-12 mr-1 ml-sm-1 mr-sm-1 ml-md-3 mr-md-3 mb-4`}
                    >
                      SignUp
                    </button>
                  </>
                  // <div style={{ color: '#ffff' }}>Hello</div>
                )}
              </div>
              <div className="col-12 px-5">
                {feedback === 'letsTalk' && (
                  <p className={`${Style.feedbackMsg} text-center`}>
                    The project owner has been notified that you are interested.
                  </p>
                )}
                {feedback === 'tracking' && (
                  <p className={`${Style.feedbackMsg} text-center`}>
                    The project owner has been notified that you are tracking.
                  </p>
                )}
                {feedback === 'notInterested' && (
                  <p className={`${Style.feedbackMsg} text-center`}>
                    The project owner has been notified that you are not
                    interested.
                  </p>
                )}
                {feedback === 'view' && (
                  <p className={`${Style.feedbackMsg} text-center`}>
                    You are awaiting feedback from the project owner.
                  </p>
                )}
              </div>

              {notes && (
                <div className=" mb-24 col">
                  <div className={Style.cupidTake}>
                    <span className={Style.CupidHeading}>Cupid take</span>
                    <p className={Style.cupidPara}>{notes}</p>
                  </div>
                </div>
              )}
            </div>
            {showDropdown && (
              <DropDownComp
                options={dropdownOptions}
                dropdownclass={`${style.dropdownPosLeft} ${style.visibledropdown} col col-md-2 col-12 p-0`}
                theme="secondary"
                style={{
                  position: 'relative',
                  width: '100%',
                  top: '50px',
                }}
              />
            )}
          </div>
        </div>
      )}
    </>
  );
};

Navbar.propTypes = {
  logout: PropTypes.func.isRequired,
  fetchDmProjects: PropTypes.func.isRequired,
  createFeedback: PropTypes.func.isRequired,
  createrInfo: PropTypes.object.isRequired,
  snapId: PropTypes.string.isRequired,
  userData: PropTypes.object.isRequired,
  feedback: PropTypes.string.isRequired,
  coverStatus: PropTypes.object.isRequired,
  snapHash: PropTypes.string.isRequired,
  heightRef: PropTypes.object.isRequired,
  projectId: PropTypes.string.isRequired,
  updateSlateStatus: PropTypes.func.isRequired,
  getSlateStatus: PropTypes.func.isRequired,
  status: PropTypes.string,
  submissionId: PropTypes.string,
};

export default Navbar;
