/* eslint-disable @next/next/no-img-element */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { get } from 'lodash';
import Cover from 'sharedComponents/ProjectTemplate/bcsTemplate/Cover/cover';
import BasicInfo from 'sharedComponents/ProjectTemplate/bcsTemplate/BasicInfo/basicInfo';
import CreativeTeam from 'sharedComponents/ProjectTemplate/bcsTemplate/CreativeTeam/creativeTeam';
import Description from 'sharedComponents/ProjectTemplate/bcsTemplate/Description';
import CastMembers from 'sharedComponents/ProjectTemplate/bcsTemplate/CastMembers/castMember';
import MoodBoard from 'sharedComponents/ProjectTemplate/bcsTemplate/Moodboard/showImages';
import Videos from 'sharedComponents/ProjectTemplate/bcsTemplate/Video/videoView';
import CompareProjects from 'sharedComponents/ProjectTemplate/bcsTemplate/Compare/compareProjects';
import Poster from 'sharedComponents/ProjectTemplate/bcsTemplate/Posters/projectPosters';
import Finance from 'sharedComponents/ProjectTemplate/bcsTemplate/Finance';
import Budget from 'sharedComponents/ProjectTemplate/bcsTemplate/Budget';
import SalesEstimate from 'sharedComponents/ProjectTemplate/bcsTemplate/SalesEstimate/salesView';
import OtherDocuments from 'sharedComponents/ProjectTemplate/bcsTemplate/OtherDocuments/otherDocuments';
import QRComponent from 'sharedComponents/qrCode/qrCode';
import MobileFooter from './mobileFooter';
import Navbar from './bottomNavBar';
import Style from '../styles/snapshot.module.scss';
import InlineSvg from 'sharedComponents/inline-svg';

class BcsIndex extends PureComponent {
  constructor() {
    super();
    this.footer = React.createRef();
  }

  render() {
    const {
      t,
      coverData,
      projectPreviewData,
      imageList,
      coverStatus,
      logLineStatus,
      creativeTeamStatus,
      castMemberStatus,
      descriptionStatus,
      artWorkStatus,
      videoStatus,
      compareStatus,
      financeStatus,
      budgetStatus,
      salesEstimateStatus,
      otherDocsStatus,
      logLineTextDataStatus,
      logLineProjectStatus,
      logLineTagsStatus,
      formatStatus,
      genreStatus,
      statusFieldStatus,
      runningTimeStatus,
      settingStatus,
      treatmentDataStatus,
      scriptDataStatus,
      visionDataStatus,
      synopsisDataStatus,
      router,
      status,
      token,
      submissionId,
      snapHashData,
      logout,
      createFeedback,
      createrInfo,
      snapId,
      userData,
      feedback,
      fetchDmProjects,
      snapHash,
      // trackSnap,
      projectId,
      projectPoster,
      updateSlateStatus,
    } = this.props;
    const videoList = get(projectPreviewData, 'videos', []);
    return (
      <>
        <div ref={this.footer} className={`${Style.bcsContainer} container`}>
          {get(projectPreviewData, 'sectionsOnOff.cover', 'unLock') ===
            'unLock' &&
            coverStatus && (
              <div className="row m-0">
                <Cover t={t} coverData={coverData} />
              </div>
            )}
          {get(projectPreviewData, 'sectionsOnOff.basicInfo', 'unLock') ===
            'unLock' &&
            logLineStatus && (
              <div className="row m-0">
                <BasicInfo
                  t={t}
                  projectPreviewData={projectPreviewData}
                  snapStatus
                  logLineTextDataStatus={logLineTextDataStatus}
                  logLineProjectStatus={logLineProjectStatus}
                  logLineTagsStatus={logLineTagsStatus}
                  formatStatus={formatStatus}
                  genreStatus={genreStatus}
                  statusFieldStatus={statusFieldStatus}
                  runningTimeStatus={runningTimeStatus}
                  settingStatus={settingStatus}
                />
              </div>
            )}
          {get(projectPreviewData, 'sectionsOnOff.creativeTeam', 'unLock') ===
            'unLock' &&
            creativeTeamStatus > 0 && (
              <div className="row ml-0 mr-0 mt-5">
                <CreativeTeam
                  projectPreviewData={projectPreviewData}
                  snapStatus
                  t={t}
                />
              </div>
            )}
          {get(projectPreviewData, 'sectionsOnOff.projectDisc', 'unLock') ===
            'unLock' &&
            descriptionStatus && (
              <div className="row ml-0 mr-0 mt-5">
                <Description
                  t={t}
                  projectPreviewData={projectPreviewData}
                  snapStatus
                  snapTreatmentStatus={treatmentDataStatus}
                  snapScriptStatus={scriptDataStatus}
                  visionDataStatus={visionDataStatus}
                  synopsisDataStatus={synopsisDataStatus}
                />
              </div>
            )}
          {get(projectPreviewData, 'sectionsOnOff.poster', 'unLock') ===
            'unLock' &&
            projectPoster.length > 0 && (
              <div className="row ml-0 mr-0">
                <Poster
                  projectPreviewData={projectPreviewData}
                  snapStatus
                  t={t}
                />
              </div>
            )}
          {get(projectPreviewData, 'sectionsOnOff.castMembers', 'unLock') ===
            'unLock' &&
            castMemberStatus > 0 && (
              <div className="row ml-0 mr-0">
                <CastMembers
                  projectPreviewData={projectPreviewData}
                  snapStatus
                  t={t}
                />
              </div>
            )}
          {get(projectPreviewData, 'sectionsOnOff.artWork', 'unLock') ===
            'unLock' &&
            artWorkStatus > 0 && (
              <div className="row ml-0 mr-0">
                <MoodBoard imageList={imageList} snapStatus t={t} />
              </div>
            )}
          {get(projectPreviewData, 'sectionsOnOff.videos', 'unLock') ===
            'unLock' &&
            videoStatus > 0 && (
              <div className="row ml-0 mr-0">
                <Videos videos={videoList} snapStatus t={t} />
              </div>
            )}
          {get(
            projectPreviewData,
            'sectionsOnOff.comparableProject',
            'unLock',
          ) === 'unLock' &&
            compareStatus > 0 && (
              <div className="row ml-0 mr-0">
                <CompareProjects
                  projectPreviewData={projectPreviewData}
                  snapStatus
                  t={t}
                />
              </div>
            )}
          {get(projectPreviewData, 'sectionsOnOff.financePlan', 'unLock') ===
            'unLock' &&
            financeStatus > 0 && (
              <div className="row ml-0 mr-0">
                <Finance projectPreviewData={projectPreviewData} t={t} />
              </div>
            )}
          {get(projectPreviewData, 'sectionsOnOff.budget', 'unLock') ===
            'unLock' &&
            budgetStatus > 0 && (
              <div className="row ml-0 mr-0">
                <Budget projectPreviewData={projectPreviewData} t={t} />
              </div>
            )}
          {get(
            projectPreviewData,
            'sectionsOnOff.salesEstimateFile',
            'unLock',
          ) === 'unLock' &&
            salesEstimateStatus > 0 && (
              <div className="row ml-0 mr-0">
                <SalesEstimate
                  projectPreviewData={projectPreviewData}
                  snapStatus
                  t={t}
                />
              </div>
            )}
          {get(
            projectPreviewData,
            'sectionsOnOff.uploadFileSection',
            'unLock',
          ) === 'unLock' &&
            otherDocsStatus > 0 && (
              <div className="row ml-0 mr-0">
                <OtherDocuments
                  projectPreviewData={projectPreviewData}
                  snapStatus
                  t={t}
                />
              </div>
            )}
        </div>
        {snapId && (
          <div className="mt-4">
            <Navbar
              status={status}
              token={token}
              router={router}
              submissionId={submissionId}
              heightRef={this.footer}
              reduceHeight={100}
              logout={logout}
              createFeedback={createFeedback}
              createrInfo={createrInfo}
              snapId={snapId}
              userData={userData}
              feedback={feedback}
              fetchDmProjects={fetchDmProjects}
              footerStatus
              coverStatus={coverStatus}
              snapHash={snapHash}
              // trackSnap={trackSnap}
              projectId={projectId}
              updateSlateStatus={updateSlateStatus}
            />
          </div>
        )}
        <>
          <div className="d-block d-sm-block d-md-none">
            <MobileFooter snapHashData={snapHashData} />
          </div>
          <div
            className={`${Style.footerContainer} ${Style.backGroundImageFooter} d-none d-sm-none d-md-flex`}
          >
            <div className="col-lg-5 d-flex col-md-12 p-0">
              <div className="col-lg-5 d-flex align-items-center text-right">
                <QRComponent
                  url={`${process.env.SnapBaseUrl}project/snap/${router.query.snapshotHash}`}
                />
              </div>
              <div className="col-lg-7 d-flex align-items-center">
                <div className="col-12">
                  <div className={`${Style.footerTitleText}`}>
                    {get(snapHashData, 'cover.title') &&
                      snapHashData.cover.title}
                  </div>
                  {get(snapHashData, 'regNo') && (
                    <div className={`${Style.footerText}`} data-cy="regNo">
                      Project identifier #{snapHashData.regNo}
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div
              className={`${Style.footerArea} col-lg-4 col-md-12 d-flex align-items-center`}
            >
              <div className="col-12">
                <div className={`${Style.footerHeadingText}`}>
                  Project creator
                </div>
                {get(snapHashData, 'contactDetails.fullName') && (
                  <div className={`${Style.footerText}`} id="fullName">
                    {snapHashData.contactDetails.fullName}
                  </div>
                )}
                {get(snapHashData, 'contactDetails.email') && (
                  <div
                    className={`${Style.footerText}`}
                    id="email"
                    data-cy="email"
                  >
                    {snapHashData.contactDetails.email}
                  </div>
                )}
              </div>
            </div>
            <div className="col-lg-3 col-md-12 mt-4 d-flex align-items-center justify-content-center">
              <InlineSvg
                src="/assets/svg/smashlogowhitenew.svg"
                width="160px"
                className={`${Style.logo}`}
                alt=""
              />
            </div>
          </div>
        </>
      </>
    );
  }
}

BcsIndex.propTypes = {
  projectPreviewData: PropTypes.object.isRequired,
  t: PropTypes.func.isRequired,
  coverData: PropTypes.object.isRequired,
  imageList: PropTypes.array.isRequired,
  coverStatus: PropTypes.bool.isRequired,
  logLineStatus: PropTypes.bool.isRequired,
  creativeTeamStatus: PropTypes.bool.isRequired,
  castMemberStatus: PropTypes.bool.isRequired,
  descriptionStatus: PropTypes.bool.isRequired,
  artWorkStatus: PropTypes.bool.isRequired,
  videoStatus: PropTypes.bool.isRequired,
  compareStatus: PropTypes.bool.isRequired,
  financeStatus: PropTypes.bool.isRequired,
  budgetStatus: PropTypes.bool.isRequired,
  salesEstimateStatus: PropTypes.bool.isRequired,
  otherDocsStatus: PropTypes.bool.isRequired,
  logLineTextDataStatus: PropTypes.bool.isRequired,
  logLineProjectStatus: PropTypes.bool.isRequired,
  logLineTagsStatus: PropTypes.bool.isRequired,
  formatStatus: PropTypes.bool.isRequired,
  genreStatus: PropTypes.bool.isRequired,
  statusFieldStatus: PropTypes.bool.isRequired,
  runningTimeStatus: PropTypes.bool.isRequired,
  settingStatus: PropTypes.bool.isRequired,
  treatmentDataStatus: PropTypes.bool.isRequired,
  scriptDataStatus: PropTypes.bool.isRequired,
  visionDataStatus: PropTypes.bool.isRequired,
  synopsisDataStatus: PropTypes.bool.isRequired,
  snapHashData: PropTypes.object.isRequired,
  router: PropTypes.object.isRequired,
  createFeedback: PropTypes.func.isRequired,
  feedback: PropTypes.string.isRequired,
  // fetchFeedbackOfSnap: PropTypes.func.isRequired,
  logout: PropTypes.func.isRequired,
  createrInfo: PropTypes.object.isRequired,
  snapId: PropTypes.string.isRequired,
  userData: PropTypes.object.isRequired,
  snapHash: PropTypes.string.isRequired,
  projectId: PropTypes.string.isRequired,
  // trackSnap: PropTypes.func.isRequired,
  projectPoster: PropTypes.array.isRequired,
  updateSlateStatus: PropTypes.func.isRequired,
};

export default BcsIndex;
